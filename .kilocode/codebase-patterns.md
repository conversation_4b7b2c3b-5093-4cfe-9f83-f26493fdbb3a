# Codebase Pattern Catalog

## Pattern Examples: Express Route Module Structure

### Pattern 1: Module Initialization with Router Factory

**Found in**:
[`src/campaign/routes/index.js:32-103`](src/campaign/routes/index.js:32-103)
**Used for**: Creating Express routers with dependency injection

```javascript
const init = (
  config,
  logger,
  campaignService,
  marvelProductApi,
  userService,
  ruleService,
  vignetteService,
  contentApi,
  teamsService,
  applicationService
) => {
  const router = express.Router();

  router.get(
    '/campaign-users/:ruleId',
    can([CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW], 'OR'),
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(getCampaignUsers(ruleService))
  );

  return router;
};

module.exports = init;
```

**Key aspects**:

- Factory function accepting dependencies
- Returns configured Express router
- Middleware chaining in route definitions
- Export initialization function, not router directly

**Found in other modules**:
[`src/alert/routes/index.js:26-76`](src/alert/routes/index.js:26-76),
[`src/routes/user.js:14-116`](src/routes/user.js:14-116),
[`src/routes/health.js:4-17`](src/routes/health.js:4-17)

### Pattern 2: Module Export Structure

**Found in**: [`src/campaign/index.js:1-5`](src/campaign/index.js:1-5) **Used
for**: Module aggregation

```javascript
const routes = require('./routes');

module.exports = {
  routes
};
```

**Key aspects**:

- Single responsibility modules
- Simple re-export pattern
- Used across all feature modules

**Found in**: [`src/alert/index.js:1-5`](src/alert/index.js:1-5),
[`src/offers/index.js:1-5`](src/offers/index.js:1-5),
[`src/message-centre-campaign/index.js:1-5`](src/message-centre-campaign/index.js:1-5)

---

## Pattern Examples: CRUD API Endpoint Patterns

### Pattern 1: GET List Endpoint with Access Control

**Found in**:
[`src/campaign/routes/getAll.js:6-74`](src/campaign/routes/getAll.js:6-74)
**Used for**: Fetching filtered lists based on user permissions

```javascript
const getAll = function (campaignService) {
  return async function (req, res, next) {
    const value = res.locals.validatedQuery;

    try {
      // filter rules by pages, containers, & rule sub types that the user has access to
      const { access } = res.locals.user;
      const { containers, pages, ruleSubTypes } = access.campaigns;
      const accessContainers = {};
      const accessPages = {};
      const accessRuleSubTypes = {};

      Object.keys(containers)
        .filter(app => !EXCLUDED_APPS.includes(app))
        .forEach(app => {
          accessContainers[app] = uniq([
            ...(containers[app].view || []),
            ...(containers[app].manage || [])
          ]);
        });

      const accessQuery = {
        accessContainers,
        accessPages,
        accessRuleSubTypes
      };

      let allRules = await campaignService.getAllCampaignsByAccess(
        value,
        accessQuery
      );

      res.status(200).json(allRules.data);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(
            new CustomError(
              err.response.status,
              JSON.stringify(err.response.data)
            )
          );
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getAll;
```

**Key aspects**:

- Higher-order function pattern (service injection)
- Uses [`res.locals.validatedQuery`](src/campaign/routes/getAll.js:8) from
  validation middleware
- Access control filtering based on user permissions
- Error handling for different response types
- Pagination support with offset/limit

**Similar pattern**:
[`src/alert/routes/getAll.js:6-90`](src/alert/routes/getAll.js:6-90)

### Pattern 2: GET Single Resource with Access Check

**Found in**:
[`src/campaign/routes/getCampaign.js:4-60`](src/campaign/routes/getCampaign.js:4-60)
**Used for**: Fetching individual campaigns with permission validation

```javascript
const getCampaign = function (campaignService, ruleService) {
  return async function (req, res, next) {
    const value = res.locals.validatedParams;

    try {
      const rule = await campaignService.getCampaign(value.ruleId);

      // check user has access to view the page, container & sub type of the rule
      const { containers, pages, ruleSubTypes } =
        res.locals.user.access.campaigns;
      const hasContainerAccess =
        containers[rule.data.application] &&
        uniq([
          ...(containers[rule.data.application].view || []),
          ...(containers[rule.data.application].manage || [])
        ]).includes(rule.data.container);

      if (!hasContainerAccess || !hasPageAccess || !hasRuleSubTypeAccess) {
        next(new ForbiddenError());
        return;
      }

      // get assignees for campaign
      const assignees = await ruleService.getAssigneesForCampaign(value.ruleId);

      if (assignees && assignees.length > 0) {
        rule.data = Object.assign({ assignees: assignees }, rule.data);
      }

      res.status(200).json(rule.data);
    } catch (err) {
      // error handling
    }
  };
};
```

**Key aspects**:

- Resource-level access control checks
- Data enrichment (adding assignees)
- Early return on access denial
- Uses [`res.locals.validatedParams`](src/campaign/routes/getCampaign.js:6)

### Pattern 3: POST Create Endpoint with Workflow

**Found in**:
[`src/campaign/routes/createCampaign.js:8-93`](src/campaign/routes/createCampaign.js:8-93)
**Used for**: Creating resources with business logic validation

```javascript
const createCampaign = function (
  logger,
  service,
  ruleService,
  userService,
  applicationService
) {
  return async function (req, res, next) {
    const value = res.locals.validatedBody;

    const allow = await canCreateCampaign(res.locals.user, req.body);
    if (!allow) {
      return next(new ForbiddenError());
    }

    const application = await applicationService.getApplicationByName(
      value.application
    );
    if (!application) {
      return next(new BadRequestError('Invalid application'));
    }

    // if campaign is in draft, no assignee are allowed
    if (
      req.body.assignees &&
      req.body.status === CampaignStatus.CAMPAIGN_STATUS_DRAFT
    ) {
      next(
        new BadRequestError(
          'Cannot assign user to a campaign rule in draft status.'
        )
      );
      return;
    }

    const sid = res.locals.user && res.locals.user.sid;
    value.created_by = sid;
    value.updated_by = sid;

    try {
      if (
        value.assignees &&
        value.status === CampaignStatus.CAMPAIGN_STATUS_SUBMITTED
      ) {
        const result = await ruleService.validateUsers(
          value.assignees,
          permission
        );

        if (!result.success) {
          return next(
            new BadRequestError(
              `Invalid users detected: \n\n${JSON.stringify(result.error)}`
            )
          );
        }

        const newCampaign = await service.createCampaign(value);
        const assigneeResult = await ruleService.assignUserListToCampaign(
          value.assignees,
          campaignId
        );

        return res.status(200).json(campaignData);
      }
    } catch (error) {
      logger.error({
        message: error.messsage,
        err: { code: error.code, stack: error.stack }
      });
      // detailed error handling
    }
  };
};
```

**Key aspects**:

- Workflow permission checks
  ([`canCreateCampaign`](src/campaign/routes/createCampaign.js:12))
- Multi-step validation (application, status rules)
- Automatic field population
  ([`created_by`](src/campaign/routes/createCampaign.js:33),
  [`updated_by`](src/campaign/routes/createCampaign.js:34))
- Conditional business logic by status
- Related resource management (assignees)
- Detailed error handling with logging

### Pattern 4: PUT/PATCH Update Endpoint

**Found in**:
[`src/campaign/routes/updateCampaign.js:37-137`](src/campaign/routes/updateCampaign.js:37-137)
**Used for**: Full updates with state transition validation

```javascript
const updateCampaign =
  (logger, campaignService, ruleService, applicationService) =>
  async (req, res, next) => {
    const params = res.locals.validatedParams;
    const body = res.locals.validatedBody;

    const campaignServiceResponse = await campaignService.getCampaign(
      params.ruleId
    );
    const campaignBefore = campaignServiceResponse.data;
    const allow = canUpdateCampaign(res.locals.user, campaignBefore, body);

    if (!allow) {
      return next(new ForbiddenError());
    }

    const transition = transitionFunction =>
      transitionFunction(campaignBefore, body);

    try {
      // verify user assignee list for campaign
      if (
        (transition(submittedToDraft) || transition(reviewedToDraft)) &&
        body.assignees &&
        !body.assignees.includes(campaignBefore.created_by)
      ) {
        return next(
          new BadRequestError(
            `Cannot assign users while campaign is in draft status`
          )
        );
      }

      const campaign = await campaignService.updateCampaign(
        params.ruleId,
        body
      );

      if (body.status === CampaignConstants.CAMPAIGN_STATUS_DRAFT) {
        await ruleService.removeUsersFromCampaign(campaignBefore.id);
        assignmentResult = await ruleService.assignUserToCampaign(
          campaignBefore.created_by,
          campaignBefore.id
        );
      }

      res
        .status(200)
        .json(Object.assign({ assignees: assignmentResult }, campaign.data));
    } catch (err) {
      // error handling
    }
  };
```

**Key aspects**:

- Fetch-before-update pattern
- State transition validation
- Permission re-evaluation with old and new data
- Side effects management (user assignments)
- Complex business rules enforcement

---

## Pattern Examples: Validation Patterns

### Pattern 1: Joi Schema Validation Middleware

**Found in**:
[`src/campaign/routes/validation.js:14-38`](src/campaign/routes/validation.js:14-38)
**Used for**: Request validation with Joi schemas

```javascript
const VALIDATION_TYPE = {
  BODY: 'body',
  PARAMS: 'params',
  QUERY: 'query'
};

const schemaValidationMiddleware =
  (schema, type = VALIDATION_TYPE.BODY, opts = DEFAULT_OPTS) =>
  (req, res, next) => {
    if (!Object.values(VALIDATION_TYPE).includes(type)) {
      return next(
        new CustomError(
          500,
          'Invalid joi validation type, must be: body, params, or query'
        )
      );
    }
    const { error, value } = schema.validate(req[type], opts);
    if (error) {
      return next(
        ERROR_HANDLER(`Joi validation error on req.${type}`, error, next)
      );
    }

    switch (type) {
      case VALIDATION_TYPE.BODY:
        res.locals.validatedBody = value;
        break;
      case VALIDATION_TYPE.PARAMS:
        res.locals.validatedParams = value;
        break;
      case VALIDATION_TYPE.QUERY:
        res.locals.validatedQuery = value;
        break;
    }
    next();
  };
```

**Key aspects**:

- Validates body, params, or query
- Stores validated data in
  [`res.locals`](src/campaign/routes/validation.js:25-31)
- Flexible schema and options
- Type-safe validation type enum

**Usage in routes**:
[`src/campaign/routes/index.js:40-41`](src/campaign/routes/index.js:40-41),
[`src/alert/routes/index.js:31-32`](src/alert/routes/index.js:31-32)

### Pattern 2: Joi Schema Definitions

**Found in**:
[`src/campaign/routes/validation.js:206-217`](src/campaign/routes/validation.js:206-217)
**Used for**: Complex nested validation schemas

```javascript
const createSchema = Joi.object()
  .keys(
    Object.assign({
      ...rule,
      platforms: rule.platforms.default([]),
      disabled: rule.disabled.default(false),
      urgent: rule.urgent.default(false),
      dismissable_flag: rule.dismissable_flag.default(true),
      status: rule.status.default('draft'),
      type: ruleTypeGet
    })
  )
  .fork(
    [
      'name',
      'start_at',
      'end_at',
      'container',
      'content_space',
      'content_type',
      'content_id',
      'pages',
      'application'
    ],
    schema => schema.required()
  );
```

**Key aspects**:

- Object spread for schema composition
- Default value assignment
- Required field marking with [`fork`](src/campaign/routes/validation.js:217)
- Reusable field definitions

**Field-level validators**:
[`src/campaign/routes/validation.js:43-68`](src/campaign/routes/validation.js:43-68)

### Pattern 3: Custom Joi Validators

**Found in**:
[`src/campaign/routes/validation.js:109-152`](src/campaign/routes/validation.js:109-152)
**Used for**: Complex validation with custom logic

```javascript
const byDemographic = Joi.object()
  .keys({
    age_min: Joi.alternatives()
      .try(
        Joi.number().integer().min(1).max(99),
        Joi.string()
          .allow('')
          .custom((value, helpers) => {
            if (value === '') return value;
            const num = parseInt(value, 10);
            if (isNaN(num) || num < 1 || num > 99) {
              return helpers.error('number.base');
            }
            return num;
          })
      )
      .optional(),
    age_max: Joi.alternatives().try(/* similar */)
  })
  .custom((value, helpers) => {
    // Validate that age_min <= age_max when both are provided
    if (value.age_min && value.age_max) {
      const min =
        typeof value.age_min === 'string'
          ? parseInt(value.age_min, 10)
          : value.age_min;
      const max =
        typeof value.age_max === 'string'
          ? parseInt(value.age_max, 10)
          : value.age_max;

      if (min > max) {
        return helpers.error('custom.ageRange');
      }
    }
    return value;
  })
  .messages({
    'custom.ageRange': 'Minimum age cannot be greater than maximum age'
  });
```

**Key aspects**:

- Object-level validation (cross-field)
- Type coercion with custom validator
- Custom error messages
- Flexible input handling (string or number)

---

## Pattern Examples: Service Layer / API Client Patterns

### Pattern 1: Axios HTTP Client Factory

**Found in**:
[`src/services/campaign-api-client/index.js:71-83`](src/services/campaign-api-client/index.js:71-83)
**Used for**: External API communication

```javascript
const commonReqParams = config => ({
  baseURL: config.url,
  headers: getRequestHeaders(),
  timeout: config.timeout
});

const createCampaign = ({ axios }, config, ruleData) =>
  axios.request({
    ...commonReqParams(config),
    url: '/v1/campaign-rules',
    method: 'post',
    data: addVersionToPlatformTargetingData(ruleData)
  });

const init = ({ axios }, config) => {
  return {
    createCampaign: ruleData => createCampaign({ axios }, config, ruleData),
    getAllCampaigns: query => getAllCampaigns({ axios }, config, query),
    getCampaign: ruleId => getCampaign({ axios }, config, ruleId),
    deleteCampaign: ruleId => deleteCampaign({ axios }, config, ruleId),
    updateCampaign: (ruleId, body) =>
      updateCampaign({ axios }, config, ruleId, body)
  };
};
```

**Key aspects**:

- Dependency injection (axios, config)
- Common request parameters extracted
- Factory returning method object
- Data transformation before sending

**Found in**:
[`src/services/alert-api-client/index.js`](src/services/alert-api-client/index.js),
[`src/services/content-api-client/index.js`](src/services/content-api-client/index.js)

### Pattern 2: Service Initialization with DB

**Found in**: [`src/services/index.js:25-57`](src/services/index.js:25-57)
**Used for**: Service layer composition

```javascript
const serviceInit = (logger, dbOpts) => {
  const dbClient = createDbClient(dbOpts);
  const db = dbClient.query;
  const permissionService = permissionServiceConstructor(db, logger);
  const userService = userServiceConstructor(db, logger);

  return {
    campaignApi,
    alertApi,
    contentApi,
    applicationService: applicationService(db),
    permissionService,
    userService,
    containerService: containerService(db, logger),
    pageService: pageService(dbClient),
    ruleService: campaignService =>
      ruleAssignmentService({
        dbService: db,
        logger,
        userService,
        permissionService,
        campaignService
      }),
    vignetteService: vignetteService(db, logger),
    platformService: platformService(db),
    teamsService: teamsService({ dbClient, userService })
  };
};

module.exports = serviceInit;
```

**Key aspects**:

- Centralized service initialization
- Shared dependencies (db, logger)
- Service interdependencies handled
- Returns service object

---

## Pattern Examples: Middleware Patterns

### Pattern 1: Permission Middleware

**Found in**:
[`src/permissions/middleware.js:16-46`](src/permissions/middleware.js:16-46)
**Used for**: Role-based access control

```javascript
const permissionMiddleware =
  (requiredPermissions, operator = 'AND') =>
  (req, res, next) => {
    let permissions;
    try {
      permissions = res.locals.user.permissions;
    } catch (err) {
      return next(new ForbiddenError());
    }
    if (!permissions || !requiredPermissions) {
      return next(new ForbiddenError());
    }
    if (typeof requiredPermissions === 'string') {
      requiredPermissions = [requiredPermissions];
    }
    if (Array.isArray(requiredPermissions)) {
      const isAdmin = permissions.includes(permissionsV1.PERMISSION_ADMIN);
      const hasPermissions = requiredPermissions.map(requiredPermission => {
        const superEquivalent = `${requiredPermission}_super`;
        return (
          permissions.includes(requiredPermission) ||
          permissions.includes(superEquivalent)
        );
      });
      const allowed =
        operator.toUpperCase() === 'OR'
          ? hasPermissions.includes(true)
          : !hasPermissions.includes(false);
      if (!allowed && !isAdmin) {
        return next(new ForbiddenError());
      } else {
        return next();
      }
    }
  };
```

**Key aspects**:

- AND/OR operator support
- Super permission check
- Admin bypass
- Normalizes string to array

**Usage**: [`src/campaign/routes/index.js:39`](src/campaign/routes/index.js:39),
[`src/alert/routes/index.js:30`](src/alert/routes/index.js:30)

### Pattern 2: Authentication Middleware

**Found in**:
[`src/server/middleware/authenticate.js:7-42`](src/server/middleware/authenticate.js:7-42)
**Used for**: User authentication and data loading

```javascript
const authenticateMiddleware = (logger, userService, permissionService) => {
  return async (req, res, next) => {
    if (!whitelistPath.includes(req.path)) {
      const identifier = req.session.wam.sub;
      if (!(identifier && identifier.length > 0)) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      try {
        res.locals.user = await userService.getUserBySid(identifier);

        if (!res.locals.user) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        delete res.locals.user.token;
        delete res.locals.user.token_expires;

        const promises = [
          permissionService.getPermissionsForUser(res.locals.user.id),
          permissionService.getAccessForUser(res.locals.user.id)
        ];
        const [permissions, access] = await Promise.all(promises);
        res.locals.user.permissions = permissions;
        res.locals.user.access = access;
      } catch (err) {
        logger.error({
          message: err.messsage,
          err: { code: err.code, stack: err.stack }
        });
        next(err);
      }
    }
    next();
  };
};
```

**Key aspects**:

- Whitelist for public routes
- Session-based authentication
- User data enrichment (permissions, access)
- Parallel permission loading
- Sensitive data removal

### Pattern 3: Global Error Handler

**Found in**:
[`src/server/middleware/global-error.js:2-35`](src/server/middleware/global-error.js:2-35)
**Used for**: Centralized error handling

```javascript
const globalErrorMiddleware = logger => (err, req, res, next) => {
  if (err.payload) {
    if (err.payload.request) {
      delete err.payload.request;
    }
    logger.error({
      message: err.message,
      payload: err.payload.toString(),
      stack: err.stack
    });
  }

  // bad request - mostly thrown by body-parser
  if (parseInt(Number(err.statusCode) / 100) === 4) {
    const badRequestError = new BadRequestError(err.message);
    const loggableErr = { ...err, url: req.originalUrl };
    delete loggableErr.body;
    logger.warn({
      message: 'Rejected bad request',
      error: { ...badRequestError, metadata: [loggableErr] }
    });
    return res
      .status(err.statusCode)
      .json({ data: {}, notifications: [badRequestError] });
  }

  return res.status(isNaN(err.code) ? 500 : err.code).json({
    code: err.code,
    message: err.errorMessage,
    uuid: err.uuid,
    timestamp: err.timestamp,
    metadata: err.metadata
  });
};
```

**Key aspects**:

- 4xx vs 5xx error handling
- Payload sanitization
- Structured error responses
- Request URL logging

---

## Pattern Examples: Error Handling Patterns

### Pattern 1: Custom Error Classes

**Found in**: [`src/error/error.js:5-54`](src/error/error.js:5-54) **Used for**:
Type-safe error handling

```javascript
class CustomError extends Error {
  constructor(code, message = codes[code], clientMessage) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
    this.code = code;
    this.uuid = uuid();
    this.timestamp = new Date().toISOString();
    this.errorMessage = clientMessage;
  }
}

class BadRequestError extends CustomError {
  constructor(message) {
    super(400, message);
    this.errorMessage = message;
  }
}

class UnauthorizedError extends CustomError {
  constructor() {
    super(401, 'Unauthorized', 'You need to sign in to view this page.');
  }
}

class ForbiddenError extends CustomError {
  constructor() {
    super(
      403,
      'Forbidden',
      'You do not have sufficient permissions to perform this action.'
    );
  }
}
```

**Key aspects**:

- Base CustomError class
- Specific error types
- UUID generation for tracking
- Timestamp for error occurrence
- Separate client/server messages

### Pattern 2: ERROR_HANDLER Utility

**Found in**: [`src/error/error.js:56-93`](src/error/error.js:56-93) **Used
for**: Consistent error transformation

```javascript
const ERROR_HANDLER = (errorMessage, err, next) => {
  // Custom error thrown elsewhere
  if (err instanceof CustomError) {
    next(err);
    return;
  }

  // Joi error
  if (err.isJoi) {
    const messages = err.details.map(i =>
      i.context.value
        ? `"${xss(i.context.value)}" is not valid for field "${
            i.context.label
          }" (${xss(i.message)})`
        : xss(i.message)
    );
    next(new BadRequestError(`${err.name}: ${messages.join('; ')}.`));
    return;
  }

  // Axios error
  if (err.response && err.response.data && err.response.data.message) {
    const { message, metadata } = err.response.data;
    let formattedMsg = message;
    if (Array.isArray(metadata) && metadata.length) {
      formattedMsg = `${formattedMsg}: ${metadata
        .map(i => i.message)
        .join('; ')}.`;
    }
    next(
      err.response.status === 404
        ? new NotFoundError(err.message, formattedMsg, err.response.data)
        : new InternalServerError(formattedMsg, err.response.data)
    );
    return;
  }

  // Default error
  next(new InternalServerError(err.message, err, errorMessage));
};
```

**Key aspects**:

- Multiple error type detection
- XSS sanitization for Joi errors
- Metadata extraction and formatting
- Status code-based error type selection

**Usage**: [`src/routes/user.js:25`](src/routes/user.js:25),
[`src/routes/user.js:38`](src/routes/user.js:38)

---

## Pattern Examples: Database Patterns

### Pattern 1: Knex Query Builder with Schema

**Found in**: [`src/db/index.js:4-61`](src/db/index.js:4-61) **Used for**:
Database abstraction

```javascript
const db = opts => {
  const dbInst = knex(
    opts || {
      client: 'mssql',
      connection: {
        database: dbConfig.database,
        user: dbConfig.user,
        password: dbConfig.password,
        port: dbConfig.port,
        server: dbConfig.server,
        timezone: 'utc',
        options: dbConfig.options || {}
      },
      pool: { min: 0, max: 7 }
    }
  );

  const _trxQuery = transaction => table => {
    return transaction(table).withSchema('admin');
  };

  const trx = async handler =>
    dbInst.transaction(async transaction => handler(_trxQuery(transaction)));

  const query = tableName => dbInst(tableName).withSchema('admin');

  return {
    dbInst,
    query,
    trx
  };
};
```

**Key aspects**:

- Connection configuration
- Schema-scoped queries ([`withSchema('admin')`](src/db/index.js:26))
- Transaction support with callback
- Query builder abstraction

**Transaction usage example**:

```javascript
const completedTransaction = await trx(async table => {
  await table(TABLE.t1).select(COLUMN.t1.id, 2).where(COLUMN.t1.id, 1);
  await table(TABLE.t2).select(COLUMN.t2.id, 2).where(COLUMN.t2.id, 1);
});
```

---

## Pattern Examples: Testing Patterns

### Pattern 1: Mock Object Structure

**Found in**:
[`src/campaign/routes/createCampaign.test.js:4-41`](src/campaign/routes/createCampaign.test.js:4-41)
**Used for**: Service and response mocking

```javascript
const mockCampaignApiClient = {
  createCampaign: jest.fn()
};

const mockRuleService = {
  validateUsers: jest.fn(),
  assignUserToCampaign: jest.fn(),
  assignUserListToCampaign: jest.fn()
};

const mockUserService = {
  getUser: jest.fn().mockReturnValue(['s000001'])
};

// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    validatedBody: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: ['admin']
    }
  }
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn()
};
```

**Key aspects**:

- Jest mock functions
- Pre-configured mock return values
- Method chaining support (status->json)
- res.locals structure mocking

### Pattern 2: Test Structure

**Found in**:
[`src/campaign/routes/createCampaign.test.js:54-136`](src/campaign/routes/createCampaign.test.js:54-136)
**Used for**: Organized test cases

```javascript
describe('Campaign Rules: routes > createCampaign', () => {
  beforeEach(() => {
    mockCampaignApiClient.createCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response for valid campaign data - submitted campaign', async () => {
    const mockBody = {
      name: 'sample name',
      application: 'nova'
      // ... more fields
    };
    const mockReq = {
      body: mockBody
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody
      }
    };
    const fakeResponse = {
      status: 200,
      data: {
        /* response data */
      }
    };

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });
    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(
      mockLogger,
      mockCampaignApiClient,
      mockRuleService,
      mockUserService,
      mockApplicationsService
    );
    await createCampaign(mockReq, res, mockNext);

    expect(mockCampaignApiClient.createCampaign).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });
});
```

**Key aspects**:

- Descriptive test organization
- beforeEach for mock cleanup
- Mock setup per test
- Assertion on method calls and arguments
- Testing both success and error paths

---

## Pattern Examples: Server Initialization Patterns

### Pattern 1: Express App Setup

**Found in**: [`src/server/index.js:16-174`](src/server/index.js:16-174) **Used
for**: Application configuration and middleware chain

```javascript
module.exports = (
  logger,
  config,
  userService,
  routes,
  authenticateMiddleware,
  launchDarklyService,
  redisClient
) => {
  const app = express();

  // Security
  app.use(helmet({ contentSecurityPolicy: config.contentSecurityPolicy }));
  app.use((req, res, next) => {
    res.setHeader('x-xss-protection', 1);
    res.set({
      'Cache-Control': [
        'no-store',
        'no-cache',
        'no-transform',
        'must-revalidate',
        'max-age=604800'
      ]
    });
    next();
  });

  // Logging
  app.use(reqStartTimeMiddleware());
  app.use(
    loggerMiddleware({
      logger,
      level,
      maskedReqHeaders,
      maskedResHeaders,
      ignoreLogging
    })
  );

  // Session management
  app.use(cookieParser(config.wamSsoAuth.clientSecret));
  app.use(
    session({
      /* config */
    })
  );

  // Body parsing
  app.use(compress());
  app.use(express.json({ limit: 200000 }));
  app.use(express.urlencoded({ limit: 200000, extended: true }));

  // Rate limiting
  app.use('/api', rateLimitMiddleware, totalRateLimiter, clientRateLimiter);

  // Authentication
  app.use(authenticateMiddleware);
  app.use(disableVerbTunneling(logger));

  // Routes
  routes.forEach(route => {
    app.use(route[0], route[1]);
  });

  // Error handling (last)
  app.use(globalErrorMiddleware(logger));

  return app;
};
```

**Key aspects**:

- Ordered middleware chain
- Security headers first
- Logging early
- Authentication before routes
- Global error handler last
- Dynamic route registration

### Pattern 2: Application Bootstrap

**Found in**: [`src/index.js:57-121`](src/index.js:57-121) **Used for**: App
initialization with services

```javascript
(async () => {
  // Initialize Launch Darkly client
  try {
    await launchDarklyService.init();
    logger.info({ message: 'Launch Darkly initialized successfully' });
  } catch (err) {
    logger.error({
      message: `Could not initialize Launch Darkly: ${err.message}`
    });
  }

  // Initialize Redis client
  const redisClient = redis.createClient(config.redisConnectionConfig);
  redisClient
    .on('connect', () => {
      logger.info({ message: 'Connected to redis successfully' });
    })
    .on('error', err => {
      logger.error({
        message: `Failed to connect to redis: ${err.message}`,
        code: err.code
      });
    });

  const server = Server(
    logger,
    config,
    userService,
    [
      ['/.well-known/jwks.json', routes.jwks(config.services.serviceAuth)],
      ['/health', routes.health()],
      ['/api/v1/applications', routes.application(services.applicationService)]
      // ... more routes
    ],
    authenticateMiddleware(logger, userService, permissionService),
    launchDarklyService,
    redisClient
  );

  server.listen(serverPort, () => {
    logger.info(`Admin server started on port ${serverPort}`);
  });
})();
```

**Key aspects**:

- IIFE async wrapper
- Service initialization with error handling
- Event-based connection monitoring
- Route array structure
- Server listening callback

---

## Pattern Examples: Utility Patterns

### Pattern 1: Async Error Wrapper

**Found in**: [`src/utils/index.js:3`](src/utils/index.js:3) **Used for**:
Express async route error handling

```javascript
const wrapAsync = fn => (req, res, next) =>
  Promise.resolve(fn(req, res, next)).catch(next);
```

**Key aspects**:

- Wraps async functions
- Auto-catches promise rejections
- Passes errors to Express error handler

**Usage**: [`src/campaign/routes/index.js:41`](src/campaign/routes/index.js:41),
[`src/alert/routes/index.js:32`](src/alert/routes/index.js:32)

### Pattern 2: Data Transformation Utilities

**Found in**: [`src/utils/index.js:24-46`](src/utils/index.js:24-46) **Used
for**: Common data manipulations

```javascript
const addVersionToPlatformTargetingData = (data, version = 1) => {
  const copiedData = { ...data };
  if (copiedData.platforms_targeting) {
    copiedData.platforms_targeting.forEach(platform => {
      platform.v = version;
    });
  }
  return copiedData;
};

const capitalize = string =>
  `${string.charAt(0).toUpperCase()}${string.slice(1)}`;

const dateFormat = dateString => moment(dateString).format('lll');

const mapPropToKey = (data, prop) =>
  data.reduce((o, i) => ({ ...o, [i[prop]]: i }), {});
```

**Key aspects**:

- Immutable data handling (spread operator)
- Side effect in copied data
- Array-to-object transformation
- Date formatting with moment

---

## Codebase-Wide Patterns Summary

**Module Organization**: Feature-based folders (campaign, alert, offers) with
consistent structure (routes/, index.js)

**Dependency Injection**: Services and config injected into route handlers via
factory functions

**Middleware Chain**: Permission check → Validation → Business logic → Response

**Error Handling**: Custom error classes → ERROR_HANDLER → Global middleware →
Structured JSON responses

**Testing**: Jest with comprehensive mocks, describe/test structure, beforeEach
cleanup

**API Structure**: RESTful endpoints with CRUD operations, pagination, filtering

**Security**: Permission-based access control, validation on all inputs, XSS
sanitization

**Database**: Knex query builder, schema-scoped queries, transaction support
