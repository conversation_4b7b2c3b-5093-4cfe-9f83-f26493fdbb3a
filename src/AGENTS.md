# Agent Architecture Documentation

## Overview

This document catalogs all agent-like modules, autonomous service handlers, and
orchestration components within the backend `src` directory. The architecture
follows a service-oriented pattern where specialized agents handle distinct
responsibilities in the campaign management, user administration, and permission
workflow system.

**Note:** This documentation excludes all front-end code and focuses exclusively
on backend service agents.

---

## Agent Categories

### 1. Service Layer Agents

### 2. Middleware Agents

### 3. Workflow Orchestration Agents

### 4. External API Client Agents

### 5. Domain-Specific Route Handlers

---

## 1. Service Layer Agents

Service agents encapsulate business logic and provide autonomous data
operations.

### 1.1 User Service Agent

**Location:** [`services/user-service.js`](services/user-service.js)

**Purpose:** Manages all user-related operations including CRUD, authentication,
and validation.

**Key Responsibilities:**

- User lifecycle management (create, read, update, delete)
- Bulk user creation for team operations
- User validation and status management
- User-role relationship management

**Key Methods:**

```javascript
getUser(query, options, dbClient);
getUsers(query, options);
getUsersFromSIDs(sidUserArray, query);
createUser(query, dbClient);
createUsers(userQueries);
updateUser(id, query, options);
deleteUser(id);
setUserStatusActive(id, active);
validateUser(sid);
getUserBySid(sid);
```

**Dependencies:** Database service, logging

**Configuration:** N/A

**Usage Example:**

```javascript
const user = await userService.validateUser(req.session.wam.sub);
```

---

### 1.2 Permission Service Agent

**Location:** [`services/permission-service.js`](services/permission-service.js)

**Purpose:** Autonomous permission resolution and access control management.

**Key Responsibilities:**

- Permission lookup for users
- Team-based access matrix calculation
- Application/container/page access resolution
- Multi-dimensional access control (campaigns, alerts, CCAU)

**Key Methods:**

```javascript
getPermissionsForUser(userId);
getAccessForUser(userId);
```

**Access Matrix Structure:**

- `campaigns.containers` - Container-level access per application
- `campaigns.pages` - Page-level access per application
- `campaigns.ruleSubTypes` - Rule sub-type access
- `alerts.containers` - Alert container access
- `alerts.pages` - Alert page access
- `ccau_campaign` - CCAU-specific campaign access

**Dependencies:** Database service

**Configuration:** Access levels from database

---

### 1.3 Rule Assignment Service Agent

**Location:**
[`services/rule-service/rule-assignment-service.js`](services/rule-service/rule-assignment-service.js)

**Purpose:** Orchestrates campaign and offer assignment workflows with
validation.

**Key Responsibilities:**

- User assignment to campaigns/offers
- Assignment validation with permission checks
- Assignee eligibility determination based on workflow state
- Approver management for variable mappings

**Key Methods:**

```javascript
assignUserToCampaign(userSid, campaignId);
assignUserListToCampaign(users, campaignId);
removeUsersFromCampaign(campaignId);
validateUsers(users, permission);
getPotentialAssigneesForCampaign(campaignId, teamId);
getPotentialAssigneesForOfferCampaign(offerStatus, userTeamId);
getAssigneesForCampaign(campaignId);
getApproversForVariableMapping(teamId);
```

**Assignment Logic:**

- Validates user existence and active status
- Checks permission requirements before assignment
- Prevents duplicate assignments
- Returns detailed error messages for invalid users

**Dependencies:**

- Database service
- User service
- Permission service
- Campaign service
- Logger

---

### 1.4 Teams Service Agent

**Location:** [`services/teams-service.js`](services/teams-service.js)

**Purpose:** Complex team lifecycle orchestration with cascading access and
permission management.

**Key Responsibilities:**

- Team CRUD with transactional integrity
- Access matrix management (containers, pages, rule sub-types)
- Role creation and permission mapping
- Owner/viewer role orchestration
- Team activation/deactivation with cascading effects

**Key Methods:**

```javascript
getTeam(id);
getTeams({ dbClient, flag, ...query });
getTeamOwners(query);
createTeam(id);
updateTeam(id, payload);
setTeamStatusActive(id, active, query);
deleteTeam(id);
```

**Access Management:**

- Manages 2100-field MSSQL limit with chunked inserts (330 records/chunk)
- Differential access updates (add/remove)
- Multi-table access coordination (containers, pages, rule sub-types)

**Role Orchestration:**

- Auto-creates "Team owner" and "Viewer" roles
- Maps permissions to roles based on access
- Handles role reassignment on team changes
- Placeholder permission injection for roles without permissions

**Flags for Optimization:**

- `FLAGS.SKIP_ALL` - Basic team info only
- `FLAGS.SKIP_ACCESS` - Skip access queries
- `FLAGS.SKIP_ALL_EXCEPT_TEAM_OWNERS` - Owner info only
- `FLAGS.INCLUDE_FUNCTIONALITIES` - Include functionality mapping

**Dependencies:**

- Database client with transaction support
- User service
- Permission utilities
- Logger

---

### 1.5 Vignette Service Agent

**Location:** [`services/vignette-service.js`](services/vignette-service.js)

**Purpose:** Provides campaign setup metadata aggregation.

**Key Responsibilities:**

- Aggregates reference data for campaign creation
- Coordinates multi-table lookups
- Data enrichment with product sub-categories

**Key Methods:**

```javascript
getCampaignSetupInfo();
```

**Data Provided:**

- Business lines
- Registration statuses
- Device types
- Geography (provinces)
- Products with sub-category descriptions

**Dependencies:** Database service, logger

---

### 1.6 Validation Service Agent

**Location:** [`services/validation-service.js`](services/validation-service.js)

**Purpose:** Schema validation orchestration.

**Key Responsibilities:**

- Request validation against defined schemas
- Error aggregation and formatting

**Dependencies:** Database service

---

### 1.7 Application Service Agent

**Location:**
[`services/application-service.js`](services/application-service.js)

**Purpose:** Application entity management.

**Key Responsibilities:**

- Application CRUD operations
- Application metadata retrieval

**Dependencies:** Database service

---

### 1.8 Container Service Agent

**Location:** [`services/container-service.js`](services/container-service.js)

**Purpose:** Container entity management.

**Dependencies:** Database service, logger

---

### 1.9 Page Service Agent

**Location:** [`services/page-service.js`](services/page-service.js)

**Purpose:** Page entity management.

**Dependencies:** Database client

---

### 1.10 Platform Service Agent

**Location:** [`services/platform-service.js`](services/platform-service.js)

**Purpose:** Platform metadata management.

**Dependencies:** Database service

---

### 1.11 Role Service Agent

**Location:** [`services/role-service.js`](services/role-service.js)

**Purpose:** Role entity management.

**Dependencies:** Database service, logger

---

### 1.12 Rule Type Service Agent

**Location:** [`services/rule-type-service.js`](services/rule-type-service.js)

**Purpose:** Rule type metadata management.

**Dependencies:** Database service, logger

---

### 1.13 Rule Sub-Type Service Agent

**Location:**
[`services/rule-sub-type-service.js`](services/rule-sub-type-service.js)

**Purpose:** Rule sub-type metadata management.

**Dependencies:** Database service, logger

---

## 2. Middleware Agents

Autonomous request interceptors and processors.

### 2.1 Authentication Agent

**Location:**
[`server/middleware/authenticate.js`](server/middleware/authenticate.js)

**Purpose:** Validates user sessions and enriches request context with user
data.

**Key Responsibilities:**

- Session validation via WAM SSO
- User retrieval and validation
- Permission and access enrichment
- Request context population (`res.locals.user`)

**Whitelisted Paths:**

- `/health`
- `/api/v1/authenticate/logout`
- `/.well-known/jwks.json`

**Request Enrichment:**

```javascript
res.locals.user = {
  id, name, email, sid, active, roles,
  permissions: [...],
  access: { campaigns, alerts, ccau_campaign }
}
```

**Dependencies:** Logger, user service, permission service

---

### 2.2 Rate Limiter Agents

**Location:**
[`server/middleware/rate-limiters.js`](server/middleware/rate-limiters.js)

**Purpose:** Dual-layer rate limiting with dynamic configuration.

**Agents:**

#### 2.2.1 Total Rate Limiter

- **Scope:** Per-instance IP address
- **Configuration:** LaunchDarkly dynamic (`req.ldRateLimitConfig.overall`)
- **Window:** Configurable (from manifest)
- **Action:** Returns 429 status on breach

#### 2.2.2 Client Rate Limiter

- **Scope:** Per true client IP (`req.trueClientIp`)
- **Configuration:** LaunchDarkly dynamic (`req.ldRateLimitConfig.client`)
- **Fallback:** Disabled if client IP unavailable (max = 0)
- **Action:** Returns 429 status on breach

**Key Methods:**

```javascript
totalRateLimiter(rateLimitConfigs, logger);
clientRateLimiter(rateLimitConfigs, logger);
```

**Dependencies:**

- Express-rate-limit
- LaunchDarkly service
- Logger

---

### 2.3 Global Error Handler Agent

**Location:**
[`server/middleware/global-error.js`](server/middleware/global-error.js)

**Purpose:** Centralized error processing and response formatting.

**Key Responsibilities:**

- 4xx error detection and handling
- Error payload sanitization
- Structured error responses
- Error logging with context

**Error Response Format:**

```javascript
{
  code: number,
  message: string,
  uuid: string,
  timestamp: string,
  metadata: array
}
```

**Dependencies:** Logger, custom error classes

---

### 2.4 Verb Tunneling Disabler

**Location:**
[`server/middleware/disable-verb-tunneling.js`](server/middleware/disable-verb-tunneling.js)

**Purpose:** Security agent preventing HTTP method override attacks.

**Dependencies:** Logger

---

### 2.5 Rate Limit Middleware (LaunchDarkly Integration)

**Location:**
[`server/middleware/rate-limit-middleware.js`](server/middleware/rate-limit-middleware.js)

**Purpose:** Enriches requests with LaunchDarkly-sourced rate limit
configuration.

**Dependencies:** LaunchDarkly service, logger

---

## 3. Workflow Orchestration Agents

State machine logic for campaign/offer/variable mapping workflows.

### 3.1 Permission Workflow Agent

**Location:** [`permissions/workflow/index.js`](permissions/workflow/index.js)

**Purpose:** Enforces permission-based state transitions.

**Key Methods:**

```javascript
canUpdateCampaign(user, campaignBefore, body);
canCreateCampaign(user, body);
canUpdateOffer(user, offerBefore, reqBody);
canCreateOffer(user, reqBody);
canUpdateVariableMappings(user, originalVariableMappingSet, body);
canCreateVariableMappingDraft(user);
canUpdateVariableMappingDraft(user);
```

**Campaign Workflow Transitions:**

- Draft → Submitted (requires `CAMPAIGNS_MANAGE` or `CCAU_CAMPAIGNS_MANAGE`)
- Submitted → Draft (requires `CAMPAIGNS_REVIEW` or `CCAU_CAMPAIGNS_REVIEW`)
- Submitted → Reviewed (requires `CAMPAIGNS_REVIEW` or `CCAU_CAMPAIGNS_REVIEW`)
- Reviewed → Draft (requires `CAMPAIGNS_APPROVE` or `CCAU_CAMPAIGNS_APPROVE`)
- Reviewed → Published (requires `CAMPAIGNS_APPROVE` or
  `CCAU_CAMPAIGNS_APPROVE`)
- Published → Disabled (requires `CAMPAIGNS_MANAGE` or `CCAU_CAMPAIGNS_MANAGE`)
- Published → Enabled (requires `CAMPAIGNS_MANAGE` or `CCAU_CAMPAIGNS_MANAGE`)
- Published → Terminated (requires `CAMPAIGNS_MANAGE` or
  `CCAU_CAMPAIGNS_MANAGE`)

**Offer Workflow Transitions:**

- Draft → Submitted (requires `OFFERS_MANAGE`)
- Submitted → Draft (requires `OFFERS_REVIEW`)
- Submitted → Reviewed (requires `OFFERS_REVIEW`)
- Reviewed → Draft (requires `OFFERS_APPROVE`)
- Reviewed → Published (requires `OFFERS_APPROVE`)
- Active → Inactive (requires `OFFERS_MANAGE`)
- Inactive → Active (requires `OFFERS_MANAGE`)

**Variable Mapping Workflow Transitions:**

- Draft → Pending (requires `PEGA_VARIABLE_MAPPING_MANAGE`)
- Draft → Deleted (requires `PEGA_VARIABLE_MAPPING_MANAGE`)
- Active → Draft (requires `PEGA_VARIABLE_MAPPING_MANAGE`)
- Pending → Draft (requires `PEGA_VARIABLE_MAPPING_APPROVE`)
- Pending → Active (requires `PEGA_VARIABLE_MAPPING_APPROVE` + approver
  validation)

**Dependencies:** Permission utilities, transaction validators

---

### 3.2 Transaction Validator Agent

**Location:**
[`permissions/workflow/transactions.js`](permissions/workflow/transactions.js)

**Purpose:** Pure state transition validation logic.

**Key Functions:**

```javascript
draftToSubmitted(oldCampaign, requestBody);
submittedToDraft(oldCampaign, requestBody);
submittedToReviewed(oldCampaign, requestBody);
reviewedToDraft(oldCampaign, requestBody);
reviewedToPublished(oldCampaign, requestBody);
disableCampaign(oldCampaign, requestBody);
enableCampaign(oldCampaign, requestBody);
terminateCampaign(oldCampaign, requestBody);
noSpecialFieldChanged(oldCampaign, newCampaign);
createdWithValidStatus(body);
activeVariableMappingSetDraft(originalVariableMappingSet, body);
draftVariableMappingSetPending(originalVariableMappingSet, body);
pendingVariableMappingSetActive(originalVariableMappingSet, body);
draftVariableMappingSetDeleted(originalVariableMappingSet, body);
pendingVariableMappingSetDraft(originalVariableMappingSet, body);
offerCreatedWithValidStatus(reqBody);
inactivateOffer(oldOffer);
activateOffer(oldOffer);
```

**Special Properties Tracked:**

- `status` - Workflow state
- `disabled` - Activation flag

**Dependencies:** None (pure functions)

---

## 4. External API Client Agents

Service clients that communicate with external systems.

### 4.1 Campaign API Client Agent

**Location:**
[`services/campaign-api-client/index.js`](services/campaign-api-client/index.js)

**Purpose:** Interfaces with external Campaign Rules API.

**Endpoints:**

- `POST /v1/campaign-rules` - Create campaign
- `GET /v1/campaign-rules` - List campaigns
- `POST /v1/campaign-rules/fetch` - List campaigns by access
- `GET /v1/campaign-rules/:ruleId` - Get campaign
- `PATCH /v1/campaign-rules/:ruleId` - Update campaign
- `DELETE /v1/campaign-rules/:ruleId` - Delete campaign

**Key Methods:**

```javascript
createCampaign(ruleData);
getAllCampaigns(query);
getAllCampaignsByAccess(query, data);
getCampaign(ruleId);
deleteCampaign(ruleId);
updateCampaign(ruleId, body);
patchCampaign(ruleId, body);
```

**Data Transformation:** Auto-adds version to platform targeting data

**Dependencies:** Axios, utility functions

**Configuration:** `config.services.ruleApi` (URL, timeout)

---

### 4.2 Alert API Client Agent

**Location:**
[`services/alert-api-client/index.js`](services/alert-api-client/index.js)

**Purpose:** Manages alert rules via external API.

**Dependencies:** Axios

**Configuration:** `config.services.ruleApi`

---

### 4.3 Content API Client Agent

**Location:**
[`services/content-api-client/index.js`](services/content-api-client/index.js)

**Purpose:** Content retrieval and management from external service.

**Dependencies:** Axios

**Configuration:** `config.services.contentApi`

---

### 4.4 Marvel Product API Client Agent

**Location:**
[`services/marvel-product-api-client/index.js`](services/marvel-product-api-client/index.js)

**Purpose:** Product data integration with LaunchDarkly feature flagging.

**Dependencies:** Axios, LaunchDarkly service, logger

**Configuration:** `config.services.marvelProductApi`

---

### 4.5 Campaign Management Client Agent

**Location:**
[`services/campaign-management-client/index.js`](services/campaign-management-client/index.js)

**Purpose:** Message centre campaign management.

**Dependencies:** Axios

**Configuration:** `config.services.campaignManagementApi`

---

### 4.6 Offers Client Agent

**Location:**
[`services/offers-client/index.js`](services/offers-client/index.js)

**Purpose:** Windmill offer management integration.

**Dependencies:** Axios

**Configuration:** `config.services.offersApi`

---

### 4.7 Variable Mappings API Client Agent (V1)

**Location:**
[`services/variable-mappings-api-client/index.js`](services/variable-mappings-api-client/index.js)

**Purpose:** PEGA variable mapping management.

**Dependencies:** Axios

**Configuration:** `config.services.ruleApi`

---

### 4.8 Variable Mappings API Client Agent (V2)

**Location:**
[`services/variable-mappings-api-client-v2/index.js`](services/variable-mappings-api-client-v2/index.js)

**Purpose:** Enhanced variable mapping API with versioning.

**Dependencies:** Axios

**Configuration:** `config.services.ruleApi`

---

### 4.9 Rule API Client Agent (V2)

**Location:** [`v2/rule-api-client/index.js`](v2/rule-api-client/index.js)

**Purpose:** Next-generation rule API integration.

**Dependencies:** Axios

**Configuration:** `config.services.ruleApi`

---

## 5. Domain-Specific Route Handlers

Express routers that act as request orchestrators.

### 5.1 Campaign Route Handler Agent

**Location:** [`campaign/routes/index.js`](campaign/routes/index.js)

**Purpose:** Campaign management request orchestration.

**Routes:**

- `GET /api/v1/campaign-rules` - List campaigns
- `GET /api/v1/export-campaign-rules` - Export campaigns
- `GET /api/v1/campaign-rules/:ruleId` - Get campaign details
- `GET /api/v1/campaign-rules/:ruleId/xml` - Get campaign as XML
- `GET /api/v1/campaign-users` - Get potential assignees
- `POST /api/v1/campaign-rules/:ruleId/setup` - Setup campaign
- `POST /api/v1/campaign-rules` - Create campaign
- `PATCH /api/v1/campaign-rules/:ruleId` - Partial update
- `PUT /api/v1/campaign-rules/:ruleId` - Full update
- `DELETE /api/v1/campaign-rules/:ruleId` - Delete campaign

**Permissions Required:**

- `CAMPAIGNS_VIEW` - View operations
- `CAMPAIGNS_MANAGE` - Mutating operations

**Dependencies:** Multiple services (campaign API, user, rule, vignette,
content, teams, application, product)

---

### 5.2 Alert Route Handler Agent

**Location:** [`alert/routes/index.js`](alert/routes/index.js)

**Purpose:** Alert rule management orchestration.

**Routes:**

- `GET /api/v1/alert-rules` - List alerts
- `GET /api/v1/export-alert-rules` - Export alerts
- `GET /api/v1/alert-rules/:ruleId` - Get alert
- `POST /api/v1/alert-rules` - Create alert
- `PATCH /api/v1/alert-rules/:ruleId` - Partial update
- `PUT /api/v1/alert-rules/:ruleId` - Full update
- `DELETE /api/v1/alert-rules/:ruleId` - Delete alert

**Permissions Required:**

- `ALERTS_VIEW` - View operations
- `ALERTS_MANAGE` - Mutating operations

**Dependencies:** Alert service, user service, permission service, content API

---

### 5.3 Offers Route Handler Agent

**Location:** [`offers/routes/index.js`](offers/routes/index.js)

**Purpose:** Windmill offer management orchestration.

**Routes:**

- `GET /api/v1/offers` - List offers
- `GET /api/v1/offers/export` - Export offers
- `GET /api/v1/offers/assignees` - Get potential assignees
- `GET /api/v1/offers/:id` - Get offer
- `POST /api/v1/offers` - Create offer
- `PUT /api/v1/offers/:id` - Update offer
- `PUT /api/v1/offers/updateOfferLocation/:id` - Update offer location
- `PUT /api/v1/offers/:id/status` - Update offer status
- `PATCH /api/v1/offers/:id` - Approve offer
- `DELETE /api/v1/offers/:id` - Delete offer

**Permissions:** Dynamically validated via workflow agent

**Dependencies:** Logger, offers service, rule service, user service

---

### 5.4 Message Centre Campaign Route Handler Agent

**Location:**
[`message-centre-campaign/routes/index.js`](message-centre-campaign/routes/index.js)

**Purpose:** Message centre campaign orchestration.

**Routes:**

- `GET /api/v1/message-centre/campaigns` - List campaigns
- `GET /api/v1/message-centre/campaigns/export` - Export campaigns
- `GET /api/v1/message-centre/campaigns/:id` - Get campaign
- `POST /api/v1/message-centre/campaigns` - Create campaign
- `PUT /api/v1/message-centre/campaigns/:id` - Update campaign
- `PATCH /api/v1/message-centre/campaigns/:id` - Partial update
- `DELETE /api/v1/message-centre/campaigns/:id` - Delete campaign

**Dependencies:** Logger, campaign management API client

---

### 5.5 Message Centre Message Route Handler Agent

**Location:**
[`message-centre-message/routes/index.js`](message-centre-message/routes/index.js)

**Purpose:** Message detail management.

**Dependencies:** Logger, campaign management API client

---

### 5.6 Variable Mappings Route Handler Agent

**Location:**
[`variable-mappings/routes/index.js`](variable-mappings/routes/index.js)

**Purpose:** PEGA variable mapping orchestration (V1 and V2).

**Routers:**

- `variableMappings` - V1 API
- `variableMappingsV2` - V2 API

**Dependencies:** Variable mappings API clients (V1 and V2), rule service

---

### 5.7 Vignette Route Handler Agent (V2)

**Location:** [`v2/vignette/index.js`](v2/vignette/index.js)

**Purpose:** Next-gen vignette campaign management.

**Routes:**

- `GET /api/v2/vignette/rules` - List rules
- `GET /api/v2/vignette/rules/:ruleId` - Get rule
- `GET /api/v2/vignette/rules/:ruleId/xml` - Get rule as XML
- `POST /api/v2/vignette/rules` - Create rule
- `PATCH /api/v2/vignette/rules/:ruleId` - Update rule
- `DELETE /api/v2/vignette/rules/:ruleId` - Delete rule

**Permissions Required:**

- `CAMPAIGNS_VIEW` - View operations
- `CAMPAIGNS_MANAGE` - Mutating operations

**Dependencies:** Logger, user service, user assignment service, rule API,
content API

---

### 5.8 E-Store Route Handler Agent (V2)

**Location:** Shared with Vignette
([`v2/vignette/index.js`](v2/vignette/index.js))

**Purpose:** E-store campaign variant using same vignette infrastructure.

**Routes:** Same as vignette but under `/api/v2/estore` path

---

## 6. Supporting Service Agents

### 6.1 Logger Service Agent

**Location:** [`services/logger/index.js`](services/logger/index.js)

**Purpose:** Structured logging with environment-aware configuration.

**Configuration:**

- Level: `info` (production/test), `debug` (development)
- Program ID: `EPM{epm}-{name}`
- Obfuscation: Configurable
- Colorization: Configurable
- Pretty print: Configurable
- SIEM integration: Configurable

**Dependencies:** `nrlw-express-scribe`

---

### 6.2 Redis Connection Agent

**Location:** [`services/redis/index.js`](services/redis/index.js)

**Purpose:** Redis connection configuration resolver.

**Configuration Strategy:**

1. If `SECRET_REDIS_AUTH_STRING` exists → TLS connection to Azure Redis
2. Else if `REDIS_URL` exists → Use provided URL
3. Else → Default to `redis://localhost:6379`

**TLS Configuration:**

- Port: 6378
- Host: `process.env.REDIS_HOST`
- Password: `process.env.SECRET_REDIS_AUTH_STRING`
- Server name: `process.env.REDIS_HOST`

---

### 6.3 Database Service Agent

**Location:** [`db/index.js`](db/index.js)

**Purpose:** Database connection and query orchestration.

**Capabilities:**

- Connection pooling
- Transaction support
- Query execution

**Dependencies:** Knex.js

---

## 7. Server Orchestration

### 7.1 Main Server Agent

**Location:** [`server/index.js`](server/index.js)

**Purpose:** Express application orchestration and middleware pipeline
management.

**Key Responsibilities:**

- Middleware pipeline configuration
- Route registration
- Security header management
- Session management (Redis-backed)
- WAM SSO authentication integration
- Static asset serving
- Proxy configuration (development mode)
- CSRF protection
- Rate limiting orchestration

**Middleware Pipeline Order:**

1. Helmet (security headers)
2. Custom security headers (XSS protection, cache control)
3. Request timing
4. Logger
5. Cookie parser
6. Session management (Redis store)
7. WAM SSO authentication
8. CSRF protection
9. Compression
10. Body parsers (JSON, URL-encoded)
11. Rate limiting (API routes only)
12. User validation
13. Proxy/static serving (environment-dependent)
14. Authentication middleware
15. Verb tunneling disabler
16. Route handlers
17. Global error handler

**Session Configuration:**

- Store: Redis
- Cookie name: `sessionID`
- Proxy-aware: Yes
- Secure: Production only
- Max age: Configurable
- Domain: Configurable

**WAM SSO Integration:**

- CSRF token management
- Session expiry detection
- CORS-aware redirect handling
- Unsecure route bypass

**Dependencies:**

- Express
- Helmet
- Compression
- Cookie parser
- Express session
- Redis store
- Hermes (WAM SSO)
- CSRF
- Multiple middleware agents
- Logger

---

## 8. Permission System

### 8.1 Permission Middleware Agent

**Location:** [`permissions/middleware.js`](permissions/middleware.js)

**Purpose:** Request-level permission enforcement.

**Key Methods:**

```javascript
middleware(requiredPermissions);
resolvePermission(user, permission);
```

**Enforcement Logic:**

- Checks `res.locals.user.permissions` against required permissions
- Returns 403 Forbidden if insufficient
- Supports array of permissions (OR logic)

**Dependencies:** Permission constants

---

### 8.2 Permission Utilities Agent

**Location:** [`permissions/utils.js`](permissions/utils.js)

**Purpose:** Access-to-permission mapping logic.

**Key Method:**

```javascript
mapAccessToRulePermissions(access, ruleTypes);
```

**Returns:**

```javascript
{
  fullPermissions: [...],  // manage permissions
  viewPermissions: [...]   // view permissions
}
```

**Dependencies:** Permission constants, Lodash

---

## 9. Application Entry Point

### 9.1 Bootstrap Agent

**Location:** [`index.js`](index.js)

**Purpose:** Application initialization orchestrator.

**Initialization Sequence:**

1. LaunchDarkly client initialization
2. Redis client connection
3. Service instantiation
4. Server creation
5. Route registration
6. Server start

**Global Error Handling:**

- Unhandled promise rejection logging
- Uncaught exception logging

**Services Initialized:**

- Application service
- Container service
- Page service
- Permission service
- Platform service
- Role service
- Rule sub-type service
- Rule type service
- Teams service
- User service
- Validation service
- Vignette service
- Variable mappings API (V1 & V2)
- Campaign API
- Content API
- Rule service
- Campaign management API
- Offers API
- Marvel product API
- Rule API client (V2)

**Routes Registered:**

- JWKS endpoint
- Health check
- Applications
- Authentication
- Users
- Containers
- Pages
- Teams
- Rule types
- Rule sub-types
- Platforms
- Roles
- Alerts
- Campaigns
- Contents
- Variable mappings (V1 & V2)
- Vignettes (V2)
- E-store (V2)
- Validation
- Message centre campaigns
- Message centre messages
- Offers

**Dependencies:**

- LaunchDarkly
- Redis
- All service agents
- Server agent
- Route handlers

---

## Agent Interaction Patterns

### Request Flow Pattern

```
Client Request
  ↓
Server Agent (middleware pipeline)
  ↓
Authentication Agent
  ↓
Rate Limiter Agents
  ↓
Route Handler Agent
  ↓
Permission Middleware Agent
  ↓
Validation
  ↓
Domain Service Agent
  ↓
External API Client Agent / Database
  ↓
Response
```

### Workflow Pattern (Campaign Update Example)

```
Route Handler
  ↓
Permission Workflow Agent.canUpdateCampaign()
  ├─ Transaction Validator Agent (state check)
  └─ Permission Service Agent (permission check)
  ↓
Campaign API Client Agent
  ↓
Response
```

### Assignment Pattern

```
Route Handler (getCampaignUsers)
  ↓
Rule Assignment Service Agent
  ├─ Campaign API Client (get campaign state)
  ├─ Permission determination
  └─ Database query (potential assignees)
  ↓
Filtered assignee list
```

---

## Configuration Dependencies

### Environment Variables

- `NODE_PATH` - Module resolution
- `PROXY_URL` - Proxy for external calls
- `SECRET_REDIS_AUTH_STRING` - Redis authentication
- `REDIS_HOST` - Redis host
- `REDIS_URL` - Redis connection URL (fallback)
- `NODE_ENV` - Environment mode
- `AUTH_S2S_PUBLIC_KEY` - Service-to-service auth
- `FRONTEND_URL` - Development proxy target

### Config Structure

Agents rely on centralized configuration object with sections:

- `config.server` - Server settings
- `config.services.ruleApi` - External API configs
- `config.services.contentApi`
- `config.services.campaignManagementApi`
- `config.services.offersApi`
- `config.services.marvelProductApi`
- `config.services.serviceAuth` - JWKS configuration
- `config.launchDarkly` - Feature flag settings
- `config.wamSsoAuth` - Authentication settings
- `config.rateLimiting` - Rate limit settings
- `config.contentSecurityPolicy` - CSP headers
- `config.logging` - Logger configuration
- `config.static` - Static file paths
- `config.redisConnectionConfig` - Redis settings

---

## Extension Guidelines

### Creating a New Service Agent

1. **Create service file** in `services/` directory
2. **Export initialization function:**
   ```javascript
   module.exports = (db, logger, ...deps) => {
     return {
       methodName: async params => {
         /* implementation */
       }
     };
   };
   ```
3. **Register in** [`services/index.js`](services/index.js)
4. **Add tests** with `.test.js` suffix
5. **Update this documentation**

### Creating a New Route Handler Agent

1. **Create route module** in appropriate domain directory
2. **Structure:**

   ```javascript
   const express = require('express');
   const { wrapAsync } = require('../../utils');
   const { middleware: can, PERMISSION } = require('../../permissions');

   const init = (config, logger, ...services) => {
     const router = express.Router();
     router.get('/path', can([PERMISSION]), wrapAsync(handler));
     return router;
   };
   module.exports = init;
   ```

3. **Register in** [`index.js`](index.js) routes array
4. **Add validation schemas**
5. **Update this documentation**

### Creating a New External API Client Agent

1. **Create client directory** in `services/`
2. **Implement** `common.js` for shared utilities
3. **Implement** `index.js` with initialization function
4. **Return object with methods:**
   ```javascript
   module.exports = ({ axios }, config) => ({
     methodName: (params) => axios.request({ ...commonReqParams(config), ... })
   });
   ```
5. **Register in** [`services/index.js`](services/index.js)
6. **Update this documentation**

---

## Testing Patterns

All agents follow consistent testing patterns:

### Service Agent Tests

- Mock database queries
- Test each public method
- Verify error handling
- Check data transformations

### Route Handler Tests

- Mock service dependencies
- Test permission enforcement
- Verify request/response formats
- Test validation schemas

### Workflow Agent Tests

- Test all state transitions
- Verify permission checks
- Test edge cases
- Ensure transaction atomicity

---

## Dependencies Matrix

| Agent                     | Database | Logger | User Service | Permission Service | External API |
| ------------------------- | -------- | ------ | ------------ | ------------------ | ------------ |
| User Service              | ✓        |        |              |                    |              |
| Permission Service        | ✓        |        |              |                    |              |
| Rule Assignment Service   | ✓        | ✓      | ✓            | ✓                  | Campaign API |
| Teams Service             | ✓        |        | ✓            |                    |              |
| Campaign Route Handler    |          | ✓      | ✓            |                    | ✓            |
| Authentication Middleware |          | ✓      | ✓            | ✓                  |              |
| Rate Limiters             |          | ✓      |              |                    | LaunchDarkly |
| Workflow Agent            |          |        |              | ✓                  |              |

---

## Architecture Principles

1. **Service Isolation** - Each agent has single responsibility
2. **Dependency Injection** - All dependencies passed via initialization
3. **Stateless Design** - No shared mutable state between agents
4. **Error Propagation** - Errors bubble up through agent chain
5. **Logging** - All agents log significant events
6. **Permission Enforcement** - Multi-layer permission checks (middleware +
   workflow)
7. **Transaction Support** - Database operations support rollback
8. **API Abstraction** - External APIs wrapped in client agents
9. **Validation First** - Schema validation before business logic
10. **Async/Await** - Consistent async pattern across agents

---

## Monitoring and Observability

### Logging Agents

All agents leverage structured logging via
[`logger service`](services/logger/index.js):

- Request/response logging with obfuscation
- Error logging with stack traces
- Performance timing
- SIEM integration support

### Error Tracking

Global error handler captures:

- 4xx client errors
- 5xx server errors
- Unhandled promise rejections
- Uncaught exceptions

### Rate Limiting Metrics

Rate limiter agents log:

- Total rate limit breaches (per instance IP)
- Client rate limit breaches (per client IP)

---

## Security Considerations

### Agent-Level Security

1. **Authentication Agent** - First line of defense, validates all requests
2. **Permission Agents** - Multi-layer authorization checks
3. **Workflow Agents** - State transition validation prevents privilege
   escalation
4. **Rate Limiters** - DDoS and abuse prevention
5. **CSRF Protection** - Token-based CSRF prevention
6. **Input Validation** - Schema validation on all inputs
7. **SQL Injection Prevention** - Parameterized queries via Knex
8. **XSS Protection** - Security headers via Helmet

### Sensitive Data Handling

- User tokens obfuscated in logs
- Password/secrets never logged
- Session data encrypted
- Redis authentication for session store

---

## Performance Optimization

### Agent Optimization Strategies

1. **Chunked Operations** - Teams service uses 330-record chunks for MSSQL
2. **Parallel Queries** - Promise.all for independent operations
3. **Query Optimization** - Selective column selection
4. **Connection Pooling** - Database connection reuse
5. **Redis Caching** - Session data cached in Redis
6. **Lazy Loading** - Team access loaded on-demand with flags

---

## Troubleshooting Guide

### Common Agent Issues

**Authentication Failures:**

- Check WAM SSO configuration
- Verify session store (Redis) connectivity
- Check user active status
- Validate CSRF token

**Permission Denials:**

- Verify user permissions in database
- Check team access matrix
- Validate workflow state transitions
- Review permission constants

**Rate Limit Issues:**

- Check LaunchDarkly connection
- Verify rate limit configuration
- Review client IP detection
- Check instance IP

**Assignment Failures:**

- Validate user existence and active status
- Check user permissions match requirement
- Verify campaign/offer state allows assignment
- Check for duplicate assignees

---

## Version History

- **Initial Version** - Comprehensive documentation of all backend agents in
  `src` directory (excluding `front-end`)

---

**Last Updated:** 2025-01-09 **Maintained By:** Development Team **Related
Documentation:**

- API Documentation
- Database Schema
- Permission Matrix
- Deployment Guide
