/* eslint-disable no-unused-expressions */
import React, { useState, useEffect, useMemo } from 'react';
import moment from 'moment';
import { useForm } from 'react-hook-form';
import { useParams, useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';

import Card from 'canvas-core-react/lib/Card';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import BackButton from 'canvas-core-react/lib/BackButton';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import Error from 'canvas-core-react/lib/internal/Error';

import {
  InputDateField,
  InputTextField,
  Input<PERSON><PERSON>r<PERSON>ield,
  InputCheckboxGroupField,
  InputSelectField,
  InputRadioGroupButtonField,
} from '../formFields/reactHookForm';
import { required, max } from '../../utils/validation';
import { addSnackbar } from '../../store/actions/snackbar';
import { addAlert } from '../../store/actions/alertBanner';
import {
  CATEGORY_OPTIONS,
  LANGUAGES_OPTIONS,
  OFFERS_FIELDS,
  INVALID_DATE_COMPARISON_ERROR_MSG,
  PRIMARY_RELATIONSHIP_RADIOBOXES,
  HUBBLE_CONTENT_SPACE,
  OFFERS_STATUS,
  ACTION,
  CANADIAN_PROVINCES,
} from './constants';
import {
  createOffer,
  getOffer,
  updateOffer,
  updateOfferLocation,
  getOfferAssignees,
  approveOffer,
  getProductBook,
} from '../../api/offers';
import { emptyStringsToNull } from '../../utils';
import permissionsList from '../../constants/permissionsList';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconAdd from 'canvas-core-react/lib/IconAdd';
import ContentModal from './contentModal';

import AdvancedTargetingSection from '../rules/advancedTargetingSection';
import SelectedContentTable from './selectedContentTable';
import OfferAsignneeSelection from './offerAssigneeSelection';
import StatusBadge from '../../components/core/statusBadge';
import { mapProductTargetingField, extractErrorDetailsArray, formatProducts } from './utils';
import MultiProvinceStateSelection from './multiProvinceStateSelection';

function OffersDetails() {
  const dispatch = useDispatch();
  const { action, id } = useParams();
  const history = useHistory();
  const [ productBook, setProductBook ] = useState([]);
  const [ isModalVisible, setIsModalVisible ] = useState(false);
  const [ offerDetails, setOfferDetails ] = useState({});
  const [ promptUserAssignment, setPromptUserAssignment ] = useState(false);
  const [ assigneeSuggestions, setAssigneeSuggestions ] = useState([]);
  const [ currentAssignees, setCurrentAssignees ] = useState([]);
  const [ initialSelections, setInitalSelections ] = useState({
    include: [],
    exclude: [],
  });
  const [ contentfulId, setContentfulId ] = useState(null);
  const [ contentfulType, setContentfulType ] = useState(null);
  const [ provinceSelectOptions, setProvinceSelectOptions ] = useState(
    CANADIAN_PROVINCES.map(province => ({ ...province })),
  );

  const mode = action || 'create';

  const authenticated = useSelector((state) => state.authenticated);
  const permissions = useMemo(() => authenticated?.permissions || {}, [ authenticated ]);

  const canEdit =
    permissions && (permissions['admin'] || permissions[permissionsList.OFFERS_MANAGE]);

  const canView = permissions && (permissions['admin'] || permissions[permissionsList.OFFERS_VIEW]);

  const canApprove =
    permissions && (permissions['admin'] || permissions[permissionsList.OFFERS_APPROVE]);

  if (!canView) {
    return <AlertBanner type="error">You do not have permissions to view this page</AlertBanner>;
  }

  const { handleSubmit, control, getValues, reset, trigger, formState, setValue } = useForm({
    mode: 'onChange',
    defaultValues: id
      ? async() =>
          getOffer(id).then(({ data }) => {
            if (mode === 'duplicate') delete data.offer_status;
            setOfferDetails(data);
            return formatData(data);
          })
      : OFFERS_FIELDS,
  });

  useEffect(() => {
    const fetchProductBook = async() => {
      const productBookRes = await getProductBook();
      setProductBook(productBookRes);
    };
    fetchProductBook();
  }, []);

  // Setting button type
  useEffect(() => {
    const observer = new MutationObserver(() => {
      const button = document.getElementById('multi-province-selection');
      if (button) {
        button.type = 'button';
        observer.disconnect(); // Stop observing once found
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect(); // Cleanup on unmount
  }, []);

  const getAssigneeSuggestions = async(status) => {
    try {
      let potentialOfferAssignees;
      if (!status) {
        potentialOfferAssignees = await getOfferAssignees();
      } else {
        potentialOfferAssignees = await getOfferAssignees({ offer_status: status });
      }

      if (potentialOfferAssignees && potentialOfferAssignees.data) {
        setAssigneeSuggestions(potentialOfferAssignees.data);
      } else {
        setAssigneeSuggestions([]);
      }
    } catch (error) {
      const errorDetails = error?.response?.data || 'Failed to fetch potential assignees for offer';
      dispatch(addAlert({ message: errorDetails }));
      setAssigneeSuggestions([]);
    }
  };

  // Use useEffect to fetch assignee suggestions in both modes
  useEffect(() => {
    const statusToUse = id && offerDetails &&
                   [ OFFERS_STATUS.DRAFT, OFFERS_STATUS.SUBMITTED ].includes(offerDetails.offer_status)
                   ? offerDetails.offer_status
                   : undefined;
    getAssigneeSuggestions(statusToUse);
  }, [ id, offerDetails ]);

  const formatData = (data) => {
    const productSelections = data?.products;

    const initialSelections = productSelections && {
      include: [
        ...(productSelections?.any_of?.map(formatProducts) || []),
        ...(productSelections.all_of?.map(formatProducts) || []),
      ],
      exclude: [
        ...(productSelections?.none_of?.map(formatProducts) || []),
      ],
    };
    setInitalSelections(initialSelections);

    setContentfulId(data.contentful_id);
    setContentfulType(data.contentful_type);

    selectProvince(false, data.location?.provinces_states);

    return {
      ...data,
      start_date: moment(data.start_date),
      ...(data.expiry_date && { expiry_date: moment(data.expiry_date) }),
      product_relationship: data.product_relationship,
      contentful_space_id: HUBBLE_CONTENT_SPACE,
      contentful_id: contentfulId,
      contentful_type: contentfulType,
      offer_status: data.offer_status,
    };
  };

  const isFormDisabled = () => {
    // Disabling based on users' permission
    if (mode === 'create' || mode === 'duplicate') return !canEdit;
    // View mode
    if (mode === 'view') return canView;
    // Disabling based on offer status
    const status = offerDetails.offer_status;
    if (status && status === OFFERS_STATUS.DRAFT) {
      return false;
    } else {
      return true;
    }
  };

  const handleOfferStatusChange = () => {
    const currentOfferStatus = offerDetails.offer_status;
    if (!currentOfferStatus || currentOfferStatus === OFFERS_STATUS.DRAFT) {
      return submitOfferForReview();
    } else if (currentOfferStatus === OFFERS_STATUS.SUBMITTED) {
      return handleApproveOffer();
    } else if (currentOfferStatus === OFFERS_STATUS.ACTIVE && mode === 'edit') {
      handleChangeApprovedOfferLocation();
    }
  };

  const hideOfferAssignmentPopup = () => {
    setPromptUserAssignment(false);
  };

  const handleAssigneeChange = (assignees) => {
    setCurrentAssignees(assignees);
    setAssigneeSuggestions(assigneeSuggestions.filter((assignee) => !assignees.includes(assignee)));
  };

  const handleApproveOffer = async() => {
    if (promptUserAssignment) {
      setPromptUserAssignment(false);
      const payload = {
        approvers: currentAssignees,
        action: ACTION.APPROVE,
      };
      try {
        const respStatus = await approveOffer(offerDetails.offer_id, payload);
        if (respStatus && respStatus === 204) {
          dispatch(
            addSnackbar({
              message: `Offer "${offerDetails.offer_id}" has been approved successfully`,
            }),
          );
          const updatedOfferDetails = { ...offerDetails, offer_status: OFFERS_STATUS.REVIEWED };
          setOfferDetails(updatedOfferDetails);
          return reset(formatData(updatedOfferDetails));
        }
      } catch (error) {
        const detailsArray = extractErrorDetailsArray(error);
        if (detailsArray.length > 0) {
          // Show all messages, e.g. as a list or joined string
          detailsArray.forEach((msg) => dispatch(addAlert({ message: msg })));
        } else {
          dispatch(addAlert({ message: 'Failed to approve offer' }));
        }
      }
    } else {
      setPromptUserAssignment(true);
    }
  };

  const handleChangeApprovedOfferLocation = async() => {
    if (promptUserAssignment) {
      setPromptUserAssignment(false);
    const payload = {
          location: getValues('location'),
          approvers: currentAssignees,
        };
    try {
      const { updatedOffer } = await updateOfferLocation(offerDetails.offer_id, payload);
      if (updatedOffer) {
        dispatch(
          addSnackbar({
            message: `Offer "${offerDetails.offer_id}" has been updated successfully`,
          }),
        );
        const updatedOfferDetails = { ...offerDetails, offer_status: OFFERS_STATUS.UPDATED };
        setOfferDetails(updatedOfferDetails);
        return reset(formatData(updatedOfferDetails));
      }
    } catch (error) {
      const detailsArray = extractErrorDetailsArray(error);
      if (detailsArray.length > 0) {
        // Show all messages, e.g. as a list or joined string
        detailsArray.forEach((msg) => dispatch(addAlert({ message: msg })));
      } else {
        dispatch(addAlert({ message: 'Failed to approve offer' }));
      }
    }
  } else {
    setPromptUserAssignment(true);
  }
  };

  const publishOffer = async() => {
    const payload = {
      action: ACTION.PUBLISH,
    };
    try {
      const respStatus = await approveOffer(offerDetails.offer_id, payload);
      if (respStatus && respStatus === 204) {
        dispatch(
          addSnackbar({
            message: `Offer "${offerDetails.offer_id}" has been published successfully`,
          }),
        );
        const updatedOfferDetails = { ...offerDetails, offer_status: OFFERS_STATUS.ACTIVE };
        setOfferDetails(updatedOfferDetails);
        return reset(formatData(updatedOfferDetails));
      }
    } catch (error) {
      const detailsArray = extractErrorDetailsArray(error);
      if (detailsArray.length > 0) {
        // Show all messages, e.g. as a list or joined string
        detailsArray.forEach((msg) => dispatch(addAlert({ message: msg })));
      } else {
        dispatch(addAlert({ message: 'Failed to publish offer' }));
      }
    }
  };

  const rejectOffer = async() => {
    const payload = { action: ACTION.REJECT };
    try {
      const respStatus = await approveOffer(offerDetails.offer_id, payload);
      if (respStatus && respStatus === 204) {
        dispatch(
          addSnackbar({
            message: `Offer "${offerDetails.offer_id}" has been rejected successfully`,
          }),
        );
        const updatedOfferDetails = { ...offerDetails, offer_status: offerDetails.offer_status === OFFERS_STATUS.UPDATED ? OFFERS_STATUS.ACTIVE : OFFERS_STATUS.DRAFT };
        setOfferDetails(updatedOfferDetails);
        reset(formatData(updatedOfferDetails));
        return history.push(`/offers/${updatedOfferDetails.offer_id}/edit`);
      }
    } catch (error) {
      const detailsArray = extractErrorDetailsArray(error);
      if (detailsArray.length > 0) {
        // Show all messages, e.g. as a list or joined string
        detailsArray.forEach((msg) => dispatch(addAlert({ message: msg })));
      } else {
        dispatch(addAlert({ message: 'Failed to reject offer' }));
      }
    }
  };

  const submitOfferForReview = async() => {
    const isValid = await trigger();
    if (isValid) {
      if (promptUserAssignment) {
        setPromptUserAssignment(false);
        handleSubmit(handleForm)();
      } else {
        setPromptUserAssignment(true);
      }
    } else {
      setPromptUserAssignment(false);
    }
  };

  const renderOfferAssignmentPopup = () => {
    return (
      <OfferAsignneeSelection
        isOpen={promptUserAssignment}
        onCancel={hideOfferAssignmentPopup}
        onSubmit={handleOfferStatusChange}
        handleAssigneeChange={handleAssigneeChange}
        potentialAssignees={assigneeSuggestions}
        setPotentialAssignees={setAssigneeSuggestions}
        assignees={currentAssignees}
      />
    );
  };

  const handleProvinceSelecting = (selectedProvinceList) => {
    setProvinceSelectOptions(selectedProvinceList);
    const selectedProvinceValues = [
      ...selectedProvinceList
        .filter((province) => province.checked)
        .map((province) => province.value),
    ];
    const location = {
      country: 'CA',
      ...(selectedProvinceValues.length > 0 && { provinces_states: selectedProvinceValues }),
    };
    setValue('location', location);
  };

  const handleProvinceSelectionChange = (checked, item, index, value, selectedItems, event) => {
    const cloneOptions = [ ...provinceSelectOptions ];
    const selectedOption = cloneOptions.findIndex(option => option.name === item.name);
    cloneOptions[selectedOption].checked = checked;
    handleProvinceSelecting(cloneOptions);
  };

  const uncheckProvinceSelection = (idx) => {
    const cloneProvinceSelectOptions = [ ...provinceSelectOptions ];
    if (typeof idx === 'string') {
      const province = cloneProvinceSelectOptions.find((province) => province.value === idx);
      if (province && 'checked' in province) {
        province.checked = false;
      }
    } else {
      cloneProvinceSelectOptions.map((province) => {
        if ('checked' in province) {
          province.checked = false;
        }
      });
    }
    handleProvinceSelecting(cloneProvinceSelectOptions);
  };

  const selectProvince = (selectAll = false, selected = []) => {
    const cloneProvinceSelectOptions = [ ...provinceSelectOptions ];
    if (selectAll) {
      cloneProvinceSelectOptions.map((province) => {
        province.checked = true;
      });
    } else if (!selectAll && selected.length > 0) {
      selected.map((val) => {
        const province = cloneProvinceSelectOptions.find((province) => province.value === val);
        province.checked = true;
      });
    }
    handleProvinceSelecting(cloneProvinceSelectOptions);
  };

  const handleProvinceTagDelete = (deletedIndex) => {
    uncheckProvinceSelection(deletedIndex);
  };

  const handleForm = async(values, e) => {
    if (e) {
      e.preventDefault();
      trigger();
    }

    const offerStatus = offerDetails?.offer_status;

    /* istanbul ignore next */
    if (!contentfulId) {
      const contentfulField = document.querySelector('.offers-details__content-field');
      contentfulField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      setTimeout(() => {
        contentfulField.focus();
      }, 400);
      return;
    }

    /* istanbul ignore next */
    if (!values.products) {
      const productField = document.querySelector('.advanced-targeting');
      productField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      setTimeout(() => {
        productField.focus();
      }, 400);
      return;
    }

    /* istanbul ignore next */
    if (!values.product_relationship) {
      const productRelationshipField = document.querySelector('.offers-details__product-relationship');
      productRelationshipField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      setTimeout(() => {
        productRelationshipField.focus();
      }, 400);
      return;
    }

    const data = emptyStringsToNull({
      title: values.title,
      category: values.category.toUpperCase(),
      priority: values.priority,
      start_date: new Date(values.start_date),
      ...(values.expiry_date && { expiry_date: new Date(values.expiry_date) }),
      target_language: values.target_language,
      products: values.products,
      product_relationship: values.product_relationship,
      contentful_space_id: HUBBLE_CONTENT_SPACE,
      contentful_id: contentfulId,
      contentful_type: contentfulType,
      location: values.location,
    });

    // if Advanced Targeting is changed update the format from include exclude array to `all_of` or `any_of` or `none_of`
    /* istanbul ignore next */
    if (values.products) {
      const productTargeting = {};
      /* istanbul ignore next */
      if (values.products?.include?.length > 0) {
        productTargeting[`${values.products.includeMode}_of`] = values.products.include;
      }
      /* istanbul ignore next */
      if (values.products?.exclude?.length > 0) {
        productTargeting['none_of'] = values.products.exclude;
      }
      // only update product if onChange of AdvancedTargeting is called
      /* istanbul ignore next */
      if (values.products.include?.length > 0 || values.products.exclude?.length > 0) {
        const mappedProductTargeting = mapProductTargetingField(productTargeting);
        data.products = mappedProductTargeting;
      }
    }

    // Get the approvers from the assignees list when the users click on 'Submit for Review'
    if ((!offerStatus || offerStatus === OFFERS_STATUS.DRAFT) && currentAssignees.length > 0) {
      data.reviewers = currentAssignees;
    }

    const formatAction = mode === 'edit' ? 'edited' : 'created';
    try {
      mode === 'create' || mode === 'duplicate'
        ? await createOffer(data).then((res) => {
            reset(formatData(res.data));
            setOfferDetails(res.data);
            return history.push(`/offers/${res.data.offer_id}/edit`);
          })
        : await updateOffer(id, data).then((res) => {
            setOfferDetails(res.updatedOffer.data);
            return reset(formatData(res.updatedOffer.data));
          });
      dispatch(
        addSnackbar({
          message: `Offer "${values.title}" has been ${formatAction} successfully`,
        }),
      );
    } catch (error) {
      const errorDetailsArray = extractErrorDetailsArray(error);
      if (errorDetailsArray.length > 0) {
        // Show all messages, e.g. as a list or joined string
        errorDetailsArray.forEach((msg) => dispatch(addAlert({ message: msg })));
      } else {
        dispatch(addAlert({ message: 'Failed to save offer' }));
      }
    }
  };

  const setModalVisible = () => {
    setIsModalVisible(!isModalVisible);
  };

  if (formState.isLoading) {
    return <IconSpinner size={32} />;
  }

  const handleTargetingChange = (value) => {
    setValue('products', value);
  };

  const handleContentSelection = ({ activeContentId, activeContentType }) => {
    setContentfulId(activeContentId);
    setContentfulType(activeContentType);
    setIsModalVisible(false);
  };

  const deleteContent = () => {
    setContentfulId(null);
    setContentfulType(null);
  };

  /* istanbul ignore next */
  const handleInvalidForm = (errors) => {
    if (errors.priority) {
      const sliderField = document.querySelector('.offers-details__information_priority');
      sliderField?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      setTimeout(() => {
        sliderField.focus();
      }, 350);
    }
  };

  return (
    <>
      <div className="offer-details__assignment-popup">{ renderOfferAssignmentPopup() }</div>
      <div className="offers-details">
        <form onSubmit={handleSubmit(handleForm, handleInvalidForm)}>
          <div className="offers-details__action-bar">
            <TextIntroduction component="h1" className="offers-details__header">
              { mode === 'create' || mode === 'duplicate' ? (
                <> Create a New Offer </>
              ) : mode === 'edit' ? (
                <> Edit Offer </>
              ) : (
                <>View Offer</>
              ) }
            </TextIntroduction>
          </div>
          { offerDetails.offer_status && (
            <TextIntroduction component="h3">
              Current Status:{ ' ' }
              <StatusBadge
                status={offerDetails.offer_status || OFFERS_STATUS.DRAFT}
                disabled={offerDetails.disabled}
                startTime={offerDetails.start_at}
                endTime={offerDetails.end_at}
              />
            </TextIntroduction>
          ) }
          <Card className="offers-details__card" type="floatLow">
            <TextHeadline component="h2" size={21} className="offers-details__sub-header">
              Offer Information
            </TextHeadline>
            <div className="offers-details__information_fields">
              <InputTextField
                control={control}
                disabled={isFormDisabled()}
                className="offers-details__information_field"
                name="title"
                label="Offer Title"
                placeholder="Enter a unique title"
                data-testid="input-offer-title"
                rules={{
                  validate: {
                    max: max(94),
                    required,
                  },
                }}
              />
              <InputSelectField
                control={control}
                className="offers-details__information_category"
                name="category"
                label={
                  <>
                    Category{ ' ' }
                    <Tooltip
                      id="categories-toollip"
                      infoButtonLabel="Categories info"
                      closeButtonLabel="close"
                      heading={'Categories'}
                    >
                      <p className="offers-details__information_category__tooltip">
                        <b>Benefits</b>: Enriched embedded features that come directly from the
                        card, covered under annual fee
                      </p>
                      <p className="offers-details__information_category__tooltip">
                        <b> Offers</b>: Exclusive access to promotional offers of varying types:
                        spending offers, discounts, one-time codes, etc.
                      </p>
                      <p className="offers-details__information_category__tooltip">
                        <b>Perks</b>: Access to special benefits based on client relationship
                      </p>
                    </Tooltip>
                  </>
                }
                placeholder="Select a category"
                disabled={isFormDisabled()}
                data-testid="select-offer-category"
                options={CATEGORY_OPTIONS}
                optionValue="value"
                rules={{
                  validate: {
                    required,
                  },
                }}
              />
            </div>
            <div className="offers-details__information_priority">
              <InputSliderField
                control={control}
                name="priority"
                label="Priority Weight"
                min={0}
                max={50}
                supplementaryTextLeft={{ label: '0 (lowest priority)' }}
                supplementaryTextRight={{ label: '50 (highest priority)' }}
                showError={formState.isSubmitted}
                disabled={isFormDisabled()}
                rules={{
                  validate: {
                    required: (value) =>
                      value && Number(value) > 0
                        ? undefined
                        : 'Please enter an integer between 1 to 50',
                  },
                }}
              />
            </div>
          </Card>

          <Card className="offers-details__card" type="floatLow">
            <TextHeadline component="h2" size={21} className="offers-details__sub-header">
              Targeting Dimension
            </TextHeadline>
            <div className="offers-details__date-fields">
              <InputDateField
                control={control}
                disabled={isFormDisabled()}
                name="start_date"
                className="offers-details__date-field"
                rules={{
                  validate: {
                    requiredDate: (value) =>
                      !value || !moment(value).isValid() ? 'Valid date is required' : undefined,
                  },
                }}
                label="Start date"
                placeholder="MM/DD/YYYY"
              />
              <InputDateField
                control={control}
                disabled={isFormDisabled()}
                name="expiry_date"
                className="offers-details__date-field"
                label="Expiry date (Optional)"
                placeholder="MM/DD/YYYY"
                rules={{
                  validate: {
                    moreThanStartDate: (value) =>
                      moment(getValues('start_date')) &&
                      moment(value).isSameOrBefore(moment(getValues('start_date')))
                        ? INVALID_DATE_COMPARISON_ERROR_MSG
                        : undefined,
                  },
                }}
              />
            </div>
            { !contentfulId ? (
              <div className="offers-details__content-field">
                <TextButton
                  Icon={IconAdd}
                  iconPosition="left"
                  type="button"
                  onClick={() => setIsModalVisible(true)}
                  data-testid="offers-add-content-button"
                >
                  Add content
                </TextButton>
                { formState.isSubmitted && !contentfulId && (
                  <div>
                    <Error errorLabel="error" errorMsg="Content required" />
                  </div>
                ) }
              </div>
            ) : (
              <div className="offers-details__fields">
                <SelectedContentTable
                  contentfulId={contentfulId}
                  contentfulType={contentfulType}
                  setModalVisible={setModalVisible}
                  formDisabled={mode === 'view'}
                  deleteContent={deleteContent}
                />
              </div>
            ) }
            <div>
              <InputCheckboxGroupField
                control={control}
                disabled={isFormDisabled()}
                label={'Languages'}
                name="target_language"
                options={LANGUAGES_OPTIONS}
                checkBoxContainerClassName="offers-details__input-group-language"
                inline={true}
              />
            </div>
          </Card>
          { /* Target by demographic section starts */ }
          <Card className="offers-details__card" type="floatLow">
            <TextHeadline component="h2" size={21} className="offers-details__sub-header">
              Target by demographic
            </TextHeadline>
            <MultiProvinceStateSelection
              name="multi-province-selection"
              className="multi-province-selection__province-fields"
              label="Canadian Province (Optional)"
              placeholder="Select Canadian province(s)"
              disabled={isFormDisabled() && !(mode === 'edit' && offerDetails.offer_status === OFFERS_STATUS.ACTIVE)}
              selectOptions={provinceSelectOptions}
              onChange={handleProvinceSelectionChange}
              onClickClearAll={uncheckProvinceSelection}
              onClickSelectAll={selectProvince}
              onClickTagDeletion={handleProvinceTagDelete}
              rules={{
                validate: {
                  required,
                },
              }}
            />
          </Card>
          { /* Target by demographic section ends */ }
          <div className="offers-details__card">
            <AdvancedTargetingSection
              productBook={productBook}
              isOptional
              includeSearch
              includeSelectionPreview
              onChange={handleTargetingChange}
              disableOwnershipSelection
              selectOnlyIncludeorExclude
              initialSelections={initialSelections}
              disabled={isFormDisabled()}
              defaultIncludeMode={offerDetails.products?.all_of ? 'all' : 'any'}
              productRelationShipCheckBoxes={
                <div className="offers-details__product-relationship">
                  { PRIMARY_RELATIONSHIP_RADIOBOXES.map((radioButton, index) => {
                    return (
                      <InputRadioGroupButtonField
                        key={index}
                        control={control}
                        options={radioButton.options}
                        name={radioButton.name}
                        label={`${radioButton.label}:`}
                        inline={true}
                        labelLeft={true}
                        disabled={isFormDisabled()}
                        rules={{
                          validate: {
                            required,
                          },
                        }}
                      />
                    );
                  }) }
                </div>
              }
            />
          </div>
          <div className="offers-details__action-buttons">
            <BackButton onClick={() => history.goBack()} type="button">
              Cancel
            </BackButton>
            <div className={'offer-details__action-buttons__submit-buttons'}>
              { (!offerDetails.offer_status || offerDetails.offer_status === OFFERS_STATUS.DRAFT) &&
                canEdit && (
                  <SecondaryButton
                    className="offer-details__action-button"
                    disabled={!canEdit}
                    data-testid="offers-save-draft-button"
                    type="submit"
                  >
                    Save as Draft
                  </SecondaryButton>
                ) }
              { mode !== 'create' &&
                mode !== 'duplicate' &&
                (!offerDetails.offer_status ||
                  offerDetails.offer_status === OFFERS_STATUS.SUBMITTED ||
                  offerDetails.offer_status === OFFERS_STATUS.REVIEWED ||
                  offerDetails.offer_status === OFFERS_STATUS.UPDATED) &&
                canApprove && (
                  <SecondaryButton
                    className="offer-details__action-button"
                    type="button"
                    disabled={!canApprove && offerDetails.offer_status !== OFFERS_STATUS.DRAFT}
                    onClick={rejectOffer}
                  >
                    Reject
                  </SecondaryButton>
                ) }
              { (!offerDetails.offer_status || offerDetails.offer_status === OFFERS_STATUS.DRAFT || (mode === 'edit' && offerDetails.offer_status === OFFERS_STATUS.ACTIVE)) &&
                canEdit && (
                  <PrimaryButton
                    className="offers-details__action-button"
                    disabled={!canEdit}
                    type="button"
                    onClick={submitOfferForReview}
                  >
                    Submit for Review
                  </PrimaryButton>
                ) }
              { offerDetails.offer_status === OFFERS_STATUS.SUBMITTED && canApprove && (
                <PrimaryButton
                  className="offers-details__action-button"
                  disabled={!canApprove}
                  type="button"
                  onClick={handleApproveOffer}
                >
                  Approve
                </PrimaryButton>
              ) }
              { (offerDetails.offer_status === OFFERS_STATUS.REVIEWED ||
                offerDetails.offer_status === OFFERS_STATUS.UPDATED) &&
                canApprove && (
                  <PrimaryButton
                    className="offers-details__action-button"
                    disabled={!canApprove}
                    type="button"
                    onClick={publishOffer}
                  >
                    Publish
                  </PrimaryButton>
                ) }
            </div>
          </div>
        </form>
      </div>
      <ContentModal
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        onClose={setModalVisible}
        handleContentSelection={handleContentSelection}
      />
    </>
  );
}

export default OffersDetails;
