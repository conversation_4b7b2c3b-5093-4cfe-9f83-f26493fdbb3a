{"name": "pigeon-admin", "version": "1.25.1", "engines": {"node": "22.13.1", "npm": "^10.9.2"}, "scripts": {"lint": "eslint src", "lint:fix": "eslint --fix src", "lint:front-end": "npm run lint --prefix front-end", "stylelint:front-end": "npm run stylelint --prefix front-end", "lint:all": "npm run lint:front-end && npm run lint", "test": "jest --runInBand --detect<PERSON><PERSON>Handles", "test:front-end": "npm run test --prefix front-end", "test:all": "npm run test:front-end && npm run test", "coverage": "npm run test:all", "build": "npm run build --prefix front-end", "migrate": "knex migrate:latest", "start": "node src/", "start:dev": "nodemon src/", "bumpVersion": "AF_VERSION=$(npm view $npm_package_name version); LATEST_VERSION=$(semver $AF_VERSION $npm_package_version | tail -1); if [ $LATEST_VERSION != $npm_package_version ] || [ $AF_VERSION = $npm_package_version ]; then npm version $AF_VERSION --force --no-git-tag-version --allow-same-version && npm version patch --force --no-git-tag-version; fi", "postinstall": "if [ \"$NODE_ENV\" == \"production\" ]; then echo \"offline=true\noptional=false\" > .npmrc; fi", "release": "standard-version"}, "dependencies": {"@testing-library/react-hooks": "^8.0.1", "axios": "1.12.1", "canvas-core-react": "13.11.0", "canvg": "^4.0.3", "classnames": "^2.3.1", "compression": "1.7.4", "connect-redis": "6.1.3", "const-express-hermes": "^1.1.0", "cookie": "^1.0.2", "cookie-parser": "^1.4.7", "core-js": "2.6.8", "css-loader": "6.9.0", "csurf": "^1.2.2", "csv-parse": "4.10.1", "dotenv": "8.0.0", "ejs": "^3.1.10", "escape-html": "1.0.3", "express": "^4.21.2", "express-rate-limit": "^6.7.0", "express-session": "^1.18.1", "helmet": "^4.4.1", "history": "4.9.0", "html2canvas": "^1.4.1", "https-proxy-agent": "5.0.0", "joi": "^17.7.0", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.2", "knex": "^2.4.2", "launchdarkly-node-server-sdk": "^7.0.1", "lodash.throttle": "^4.1.1", "math-random": "1.0.4", "moment": "^2.29.4", "moment-timezone": "^0.5.44", "nanoid": "^5.1.5", "node-fetch": "^2.6.1", "nrlw-express-scribe": "^1.15.0", "pigeon-cerberus": "^3.0.1", "pigeon-pigeon-pack": "^1.2.0", "pigeon-pigeon-web-renderer": "^4.0.17", "prop-types": "15.7.2", "qs": "^6.14.0", "ramda": "^0.29.1", "range_check": "^2.0.4", "react": "16.14.0", "react-datetime": "2.16.3", "react-dom": "16.13.1", "react-hook-form": "^7.30.0", "react-redux": "7.1.0", "react-router": "^5.1.2", "react-router-dom": "^5.1.2", "redis": "3.1.2", "redux": "4.0.4", "redux-form": "8.3.8", "redux-thunk": "2.3.0", "sass-mq": "5.0.1", "semver": "^7.5.4", "styled-components": "^5.3.1", "supertest": "^6.3.3", "tedious": "^18.6.1", "uuid": "3.3.2", "winston": "^3.11.0", "xmlbuilder": "13.0.2", "xss": "^1.0.14"}, "devDependencies": {"@babel/core": "7.26.0", "@babel/plugin-proposal-class-properties": "7.5.5", "@babel/plugin-proposal-decorators": "7.4.4", "@babel/plugin-proposal-optional-chaining": "7.12.13", "@babel/preset-env": "7.26.0", "@babel/preset-react": "7.0.0", "@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@scotia/eslint-config-scotiabank": "1.0.1", "@testing-library/jest-dom": "6.2.0", "@testing-library/react": "12.1.2", "@testing-library/user-event": "10.4.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.2", "babel-jest": "24.8.0", "babel-loader": "^8.1.0", "babel-plugin-module-resolver": "^4.0.0", "conventional-changelog-eslint": "^3.0.9", "copy-webpack-plugin": "^10.2.4", "css-minimizer-webpack-plugin": "^6.0.0", "enzyme": "3.10.0", "enzyme-adapter-react-16": "1.14.0", "enzyme-to-json": "3.3.5", "eslint": "6.8.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "22.14.0", "eslint-plugin-react": "7.14.3", "file-loader": "6.2.0", "html-loader": "^1.3.2", "html-webpack-harddisk-plugin": "2.0.0", "html-webpack-plugin": "^5.0.0", "http-proxy-middleware": "^2.0.7", "husky": "3.0.2", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-canvas-mock": "^2.5.2", "jest-dom": "^4.0.0", "jest-environment-jsdom": "29.7.0", "mini-css-extract-plugin": "1.6.2", "mock-knex": "^0.4.13", "node-polyfill-webpack-plugin": "^3.0.0", "nodemon": "^2.0.2", "redux-mock-store": "1.5.4", "redux-test-utils": "0.3.0", "sass": "1.22.9", "sass-loader": "13.3.2", "sass-mq": "5.0.1", "standard-version": "^9.2.0", "stylelint": "^16.18.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-config-standard": "^38.0.0", "webpack": "^5.88.2", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.1"}, "overrides": {"axios": "1.12.1", "brace-expansion": "2.0.2", "@azure/identity": "4.10.1", "@azure/core-tracing": "^1.0.1", "eslint-plugin-import": "^2.22.1", "unset-value": "2.0.1", "semver": "^7.5.4", "braces": "3.0.3", "form-data": "^4.0.4", "sha.js": "2.4.12"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "properties": {"artifactory_contextUrl": "https://af.cds.bns/artifactory", "artifactory_projectRepoKey": "local-npm-bns", "artifactory_user": "", "artifactory_password": "", "artifactory_npm_repo": "virtual-npm-bns", "pcf_app_url": "", "cdp_vault_name": "PIGEON", "cdp_environment_name": "", "cdp_region_name": "", "cdp_vault_clientId": "", "cdp_vault_clientSecret": "", "nodejs_tool_name": "nodejs-16.16.0", "pipeline_plugin_version": "1.2.1", "sonar_host_url": "https://sonar.agile.bns/", "sonar_sources": "front-end/src,src", "sonar_tests": "", "sonar_report_paths": "front-end/coverage/lcov.info,coverage/lcov.info", "sonar_language": "js"}, "settings": {"rootProject.name": "admin"}}